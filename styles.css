/* CSS Variables for Color Palette */
:root {
    /* Primary Colors */
    --primary-blue: #1e3a8a;
    --primary-green: #065f46;
    --primary: var(--primary-blue);
    
    /* Secondary Colors */
    --warm-grey: #f5f5f4;
    --soft-grey: #e7e5e4;
    --medium-grey: #a8a29e;
    --dark-grey: #44403c;
    
    /* Accent Colors */
    --muted-gold: #d97706;
    --warm-terracotta: #ea580c;
    --sky-blue: #0ea5e9;
    --accent: var(--muted-gold);
    
    /* Text Colors */
    --text-primary: #1c1917;
    --text-secondary: #57534e;
    --text-light: #78716c;
    
    /* Background Colors */
    --bg-white: #ffffff;
    --bg-light: var(--warm-grey);
    --bg-primary: var(--primary);
    
    /* Typography */
    --font-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
    --font-heading: 'Playfair Display', Georgia, serif;
    
    /* Spacing */
    --spacing-xs: 0.5rem;
    --spacing-sm: 1rem;
    --spacing-md: 1.5rem;
    --spacing-lg: 2rem;
    --spacing-xl: 3rem;
    --spacing-2xl: 4rem;
    
    /* Border Radius */
    --radius-sm: 0.375rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    
    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
}

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
}

body {
    font-family: var(--font-primary);
    font-size: 1rem;
    line-height: 1.6;
    color: var(--text-primary);
    background-color: var(--bg-white);
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    font-family: var(--font-heading);
    font-weight: 600;
    line-height: 1.2;
    margin-bottom: var(--spacing-sm);
    color: var(--text-primary);
}

h1 { font-size: 2.5rem; }
h2 { font-size: 2rem; }
h3 { font-size: 1.5rem; }
h4 { font-size: 1.25rem; }

p {
    margin-bottom: var(--spacing-sm);
    color: var(--text-secondary);
}

a {
    color: var(--primary);
    text-decoration: none;
    transition: color 0.3s ease;
}

a:hover {
    color: var(--accent);
}

/* Layout Components */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--spacing-md);
}

.section {
    padding: var(--spacing-2xl) 0;
}

.section-title {
    text-align: center;
    margin-bottom: var(--spacing-md);
    font-size: 2.25rem;
}

.section-subtitle {
    text-align: center;
    font-size: 1.125rem;
    color: var(--text-secondary);
    margin-bottom: var(--spacing-xl);
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

/* Grid System */
.grid {
    display: grid;
    gap: var(--spacing-lg);
    align-items: center;
}

.grid-2 {
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
}

.grid-3 {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
}

/* Background Utilities */
.bg-light {
    background-color: var(--bg-light);
}

.bg-primary {
    background-color: var(--bg-primary);
}

.text-white {
    color: white;
}

.text-white h1,
.text-white h2,
.text-white h3,
.text-white h4,
.text-white p {
    color: white;
}

.text-center {
    text-align: center;
}

/* Header and Navigation */
.header {
    background-color: var(--bg-white);
    box-shadow: var(--shadow-sm);
    position: sticky;
    top: 0;
    z-index: 1000;
}

.nav-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-sm) var(--spacing-md);
    max-width: 1200px;
    margin: 0 auto;
    min-height: 60px;
}

.nav-logo h1 {
    font-size: 1.5rem;
    color: var(--primary);
    margin: 0;
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: var(--spacing-md);
    align-items: center;
    flex-wrap: nowrap;
}

.nav-link {
    font-weight: 500;
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-md);
    transition: all 0.3s ease;
    white-space: nowrap;
    font-size: 0.9rem;
}

.nav-link:hover,
.nav-link.active {
    background-color: var(--bg-light);
    color: var(--primary);
}

.nav-link.cta-button {
    background-color: var(--accent);
    color: white;
    padding: var(--spacing-xs) var(--spacing-md);
}

.nav-link.cta-button:hover {
    background-color: var(--primary);
    color: white;
}

.nav-toggle {
    display: none;
    flex-direction: column;
    cursor: pointer;
}

.nav-toggle span {
    width: 25px;
    height: 3px;
    background-color: var(--primary);
    margin: 3px 0;
    transition: 0.3s;
}

/* Buttons */
.btn {
    display: inline-block;
    padding: var(--spacing-sm) var(--spacing-lg);
    border-radius: var(--radius-md);
    font-weight: 600;
    text-align: center;
    text-decoration: none;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
    font-size: 1rem;
}

.btn-primary {
    background-color: var(--accent);
    color: white;
}

.btn-primary:hover {
    background-color: var(--primary);
    color: white;
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.btn-secondary {
    background-color: transparent;
    color: var(--primary);
    border: 2px solid var(--primary);
}

.btn-secondary:hover {
    background-color: var(--primary);
    color: white;
}

.btn-accent {
    background-color: white;
    color: var(--primary);
}

.btn-accent:hover {
    background-color: var(--accent);
    color: white;
}

/* Hero Section */
.hero {
    padding: var(--spacing-2xl) 0;
    background: linear-gradient(135deg, var(--bg-light) 0%, white 100%);
}

.hero .container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-xl);
    align-items: center;
}

.hero-title {
    font-size: 3rem;
    margin-bottom: var(--spacing-md);
    color: var(--primary);
}

.hero-subtitle {
    font-size: 1.25rem;
    margin-bottom: var(--spacing-xl);
    color: var(--text-secondary);
}

.hero-image {
    border-radius: var(--radius-lg);
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
}

.hero-img {
    width: 100%;
    height: auto;
    max-height: 400px;
    object-fit: cover;
    border-radius: var(--radius-lg);
}

.content-image {
    border-radius: var(--radius-lg);
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
}

.section-img {
    width: 100%;
    height: auto;
    max-height: 350px;
    object-fit: cover;
    border-radius: var(--radius-lg);
}

.image-placeholder {
    background-color: var(--soft-grey);
    border-radius: var(--radius-lg);
    padding: var(--spacing-xl);
    text-align: center;
    color: var(--text-light);
    min-height: 300px;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Service Cards */
.service-card {
    background-color: white;
    padding: var(--spacing-xl);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-md);
    text-align: center;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.service-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.service-icon {
    font-size: 3rem;
    margin-bottom: var(--spacing-md);
}

.service-card h3 {
    color: var(--primary);
    margin-bottom: var(--spacing-sm);
}

/* Checklist */
.checklist {
    list-style: none;
}

.checklist li {
    position: relative;
    padding-left: var(--spacing-lg);
    margin-bottom: var(--spacing-sm);
    color: var(--text-secondary);
}

.checklist li::before {
    content: "✓";
    position: absolute;
    left: 0;
    color: var(--accent);
    font-weight: bold;
}

/* Features */
.feature {
    margin-bottom: var(--spacing-lg);
}

.feature h3 {
    color: var(--primary);
    margin-bottom: var(--spacing-xs);
}

/* Footer */
.footer {
    background-color: var(--dark-grey);
    color: white;
    padding: var(--spacing-2xl) 0 var(--spacing-lg);
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-xl);
    margin-bottom: var(--spacing-lg);
}

.footer-section h3,
.footer-section h4 {
    color: white;
    margin-bottom: var(--spacing-sm);
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: var(--spacing-xs);
}

.footer-section a {
    color: var(--medium-grey);
}

.footer-section a:hover {
    color: white;
}

.footer-bottom {
    border-top: 1px solid var(--medium-grey);
    padding-top: var(--spacing-sm);
    text-align: center;
    color: var(--medium-grey);
}

/* Quote Styles */
.quote {
    background-color: var(--bg-light);
    border-left: 4px solid var(--accent);
    padding: var(--spacing-lg);
    margin: var(--spacing-xl) 0;
    font-style: italic;
    font-size: 1.125rem;
    color: var(--text-primary);
    border-radius: var(--radius-md);
}

/* Expertise Cards */
.expertise-card {
    background-color: white;
    padding: var(--spacing-lg);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-sm);
    text-align: center;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.expertise-card:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-md);
}

.expertise-icon {
    font-size: 2.5rem;
    margin-bottom: var(--spacing-sm);
}

.expertise-card h3 {
    color: var(--primary);
    margin-bottom: var(--spacing-xs);
    font-size: 1.125rem;
}

.expertise-card p {
    font-size: 0.9rem;
    color: var(--text-light);
}

/* Commitment Items */
.commitment-item {
    margin-bottom: var(--spacing-lg);
    padding: var(--spacing-md);
    border-radius: var(--radius-md);
    background-color: var(--bg-light);
}

.commitment-item h3 {
    color: var(--primary);
    margin-bottom: var(--spacing-xs);
    font-size: 1.125rem;
}

.commitment-item p {
    color: var(--text-secondary);
    margin: 0;
}

/* Process Steps */
.process-step {
    background-color: white;
    padding: var(--spacing-xl);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-md);
    text-align: center;
    position: relative;
    transition: transform 0.3s ease;
}

.process-step:hover {
    transform: translateY(-5px);
}

.process-number {
    background-color: var(--accent);
    color: white;
    width: 3rem;
    height: 3rem;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 1.25rem;
    margin: 0 auto var(--spacing-md);
}

.process-icon {
    font-size: 3rem;
    margin-bottom: var(--spacing-md);
}

.process-step h3 {
    color: var(--primary);
    margin-bottom: var(--spacing-sm);
}

.process-step h4 {
    color: var(--text-secondary);
    font-size: 1rem;
    font-weight: 500;
    margin-bottom: var(--spacing-sm);
}

.process-list {
    list-style: none;
    text-align: left;
    margin-top: var(--spacing-md);
}

.process-list li {
    position: relative;
    padding-left: var(--spacing-md);
    margin-bottom: var(--spacing-xs);
    color: var(--text-secondary);
}

.process-list li::before {
    content: "•";
    position: absolute;
    left: 0;
    color: var(--accent);
    font-weight: bold;
}

/* Role Cards */
.role-card {
    background-color: white;
    padding: var(--spacing-xl);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-md);
}

.role-card h3 {
    color: var(--primary);
    margin-bottom: var(--spacing-md);
    text-align: center;
}

.role-list {
    list-style: none;
}

.role-list li {
    position: relative;
    padding-left: var(--spacing-md);
    margin-bottom: var(--spacing-sm);
    color: var(--text-secondary);
}

.role-list li::before {
    content: "✓";
    position: absolute;
    left: 0;
    color: var(--accent);
    font-weight: bold;
}

.partnership-statement {
    font-size: 1.125rem;
    color: var(--primary);
    font-weight: 500;
    max-width: 800px;
    margin: 0 auto;
    padding: var(--spacing-lg);
    background-color: white;
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-sm);
}

/* Support Areas */
.support-area {
    text-align: center;
    padding: var(--spacing-lg);
}

.support-icon {
    font-size: 2.5rem;
    margin-bottom: var(--spacing-sm);
}

.support-area h3 {
    color: var(--primary);
    margin-bottom: var(--spacing-xs);
    font-size: 1.125rem;
}

.support-area p {
    color: var(--text-secondary);
    font-size: 0.9rem;
}

/* Criteria Cards */
.criteria-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--spacing-lg);
    margin-top: var(--spacing-xl);
}

.criteria-card {
    background-color: white;
    padding: var(--spacing-xl);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-md);
    text-align: center;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.criteria-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.criteria-icon {
    font-size: 3rem;
    margin-bottom: var(--spacing-md);
}

.criteria-card h3 {
    color: var(--primary);
    margin-bottom: var(--spacing-sm);
    font-size: 1.25rem;
}

.criteria-card p {
    color: var(--text-secondary);
    margin: 0;
}

/* Business Types */
.business-types {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-lg);
    margin-top: var(--spacing-xl);
}

.business-category {
    background-color: white;
    padding: var(--spacing-lg);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-sm);
}

.business-category h3 {
    color: var(--primary);
    margin-bottom: var(--spacing-md);
    text-align: center;
    font-size: 1.125rem;
}

.business-list {
    list-style: none;
}

.business-list li {
    position: relative;
    padding-left: var(--spacing-md);
    margin-bottom: var(--spacing-xs);
    color: var(--text-secondary);
}

.business-list li::before {
    content: "•";
    position: absolute;
    left: 0;
    color: var(--accent);
    font-weight: bold;
}

/* Quality Items */
.qualities-content {
    padding-right: var(--spacing-lg);
}

.quality-item {
    margin-bottom: var(--spacing-lg);
    padding: var(--spacing-md);
    border-left: 4px solid var(--accent);
    background-color: var(--bg-light);
    border-radius: 0 var(--radius-md) var(--radius-md) 0;
}

.quality-item h3 {
    color: var(--primary);
    margin-bottom: var(--spacing-xs);
    font-size: 1.125rem;
}

.quality-item p {
    color: var(--text-secondary);
    margin: 0;
}

/* Self Assessment */
.self-assessment {
    max-width: 800px;
    margin: 0 auto;
}

.assessment-intro {
    text-align: center;
    margin-bottom: var(--spacing-xl);
}

.assessment-intro p {
    font-size: 1.125rem;
    color: var(--text-primary);
}

.assessment-checklist {
    background-color: white;
    padding: var(--spacing-xl);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-md);
}

.assessment-item {
    display: flex;
    align-items: flex-start;
    margin-bottom: var(--spacing-lg);
    padding: var(--spacing-md);
    border-radius: var(--radius-md);
    transition: background-color 0.3s ease;
}

.assessment-item:hover {
    background-color: var(--bg-light);
}

.check-icon {
    background-color: var(--accent);
    color: white;
    width: 1.5rem;
    height: 1.5rem;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 0.875rem;
    margin-right: var(--spacing-md);
    flex-shrink: 0;
    margin-top: 0.125rem;
}

.assessment-item p {
    color: var(--text-primary);
    margin: 0;
    font-size: 1rem;
}

/* Differentiators */
.differentiators {
    margin-top: var(--spacing-xl);
}

.differentiator {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-xl);
    align-items: center;
    margin-bottom: var(--spacing-2xl);
    padding: var(--spacing-xl);
    border-radius: var(--radius-lg);
    background-color: var(--bg-light);
}

.differentiator.reverse {
    background-color: white;
    box-shadow: var(--shadow-sm);
}

.differentiator.reverse .diff-content {
    order: 2;
}

.differentiator.reverse .diff-image {
    order: 1;
}

.diff-icon {
    font-size: 3rem;
    margin-bottom: var(--spacing-md);
}

.diff-content h3 {
    color: var(--primary);
    margin-bottom: var(--spacing-md);
    font-size: 1.5rem;
}

.diff-content p {
    color: var(--text-secondary);
    margin-bottom: var(--spacing-md);
    font-size: 1.125rem;
}

.diff-benefits {
    list-style: none;
}

.diff-benefits li {
    position: relative;
    padding-left: var(--spacing-md);
    margin-bottom: var(--spacing-xs);
    color: var(--text-secondary);
}

.diff-benefits li::before {
    content: "✓";
    position: absolute;
    left: 0;
    color: var(--accent);
    font-weight: bold;
}

/* Comparison Table */
.comparison-table {
    background-color: white;
    border-radius: var(--radius-lg);
    overflow: hidden;
    box-shadow: var(--shadow-md);
}

.comparison-header,
.comparison-row {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr 1fr;
}

.comparison-header {
    background-color: var(--primary);
    color: white;
    font-weight: 600;
}

.comparison-header .comparison-category {
    background-color: var(--dark-grey);
}

.comparison-column,
.comparison-cell,
.comparison-category {
    padding: var(--spacing-md);
    text-align: center;
    border-right: 1px solid var(--soft-grey);
}

.comparison-column:last-child,
.comparison-cell:last-child {
    border-right: none;
}

.comparison-row {
    border-bottom: 1px solid var(--soft-grey);
}

.comparison-row:last-child {
    border-bottom: none;
}

.comparison-row:nth-child(even) {
    background-color: var(--bg-light);
}

.comparison-category {
    background-color: var(--soft-grey);
    font-weight: 600;
    color: var(--text-primary);
    text-align: left;
}

.highlight {
    background-color: var(--accent) !important;
    color: white !important;
    font-weight: 600;
}

/* Success Factors */
.success-factor {
    text-align: center;
    padding: var(--spacing-lg);
    background-color: white;
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-sm);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.success-factor:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-md);
}

.factor-icon {
    font-size: 2.5rem;
    margin-bottom: var(--spacing-sm);
}

.success-factor h3 {
    color: var(--primary);
    margin-bottom: var(--spacing-xs);
    font-size: 1.125rem;
}

.success-factor p {
    color: var(--text-secondary);
    font-size: 0.9rem;
    margin: 0;
}

/* Impact Cards */
.impact-card {
    background-color: white;
    padding: var(--spacing-xl);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-md);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.impact-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.impact-icon {
    font-size: 3rem;
    margin-bottom: var(--spacing-md);
    text-align: center;
}

.impact-card h3 {
    color: var(--primary);
    margin-bottom: var(--spacing-sm);
    text-align: center;
}

.impact-card p {
    color: var(--text-secondary);
    margin-bottom: var(--spacing-md);
    text-align: center;
}

.impact-benefits {
    list-style: none;
    text-align: left;
}

.impact-benefits li {
    position: relative;
    padding-left: var(--spacing-md);
    margin-bottom: var(--spacing-xs);
    color: var(--text-secondary);
}

.impact-benefits li::before {
    content: "✓";
    position: absolute;
    left: 0;
    color: var(--accent);
    font-weight: bold;
}

/* Metrics Grid */
.metrics-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-lg);
    margin-top: var(--spacing-xl);
}

.metric-card {
    background-color: white;
    padding: var(--spacing-xl);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-md);
    text-align: center;
    transition: transform 0.3s ease;
}

.metric-card:hover {
    transform: translateY(-3px);
}

.metric-number {
    font-size: 3rem;
    font-weight: 700;
    color: var(--accent);
    margin-bottom: var(--spacing-xs);
    font-family: var(--font-heading);
}

.metric-label {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--primary);
    margin-bottom: var(--spacing-sm);
}

.metric-card p {
    color: var(--text-secondary);
    font-size: 0.9rem;
    margin: 0;
}

/* Timeline */
.journey-timeline {
    position: relative;
    max-width: 800px;
    margin: 0 auto;
}

.journey-timeline::before {
    content: '';
    position: absolute;
    left: 2rem;
    top: 0;
    bottom: 0;
    width: 2px;
    background-color: var(--accent);
}

.timeline-item {
    position: relative;
    margin-bottom: var(--spacing-xl);
    padding-left: 5rem;
}

.timeline-marker {
    position: absolute;
    left: 1rem;
    top: 0;
    width: 2rem;
    height: 2rem;
    background-color: var(--accent);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 0.875rem;
}

.timeline-content {
    background-color: white;
    padding: var(--spacing-lg);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-sm);
}

.timeline-content h3 {
    color: var(--primary);
    margin-bottom: var(--spacing-sm);
}

.timeline-content p {
    color: var(--text-secondary);
    margin-bottom: var(--spacing-sm);
}

.timeline-duration {
    font-size: 0.875rem;
    color: var(--accent);
    font-weight: 600;
}

/* Future Stories */
.future-stories {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-lg);
    margin-top: var(--spacing-xl);
}

.story-placeholder {
    background-color: white;
    padding: var(--spacing-xl);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-sm);
    text-align: center;
    border: 2px dashed var(--soft-grey);
    transition: border-color 0.3s ease;
}

.story-placeholder:hover {
    border-color: var(--accent);
}

.story-icon {
    font-size: 3rem;
    margin-bottom: var(--spacing-md);
}

.story-placeholder h3 {
    color: var(--primary);
    margin-bottom: var(--spacing-sm);
}

.story-placeholder p {
    color: var(--text-light);
    font-size: 0.9rem;
    margin: 0;
}

.future-message {
    font-size: 1.125rem;
    color: var(--text-primary);
    font-style: italic;
    max-width: 600px;
    margin: 0 auto;
    padding: var(--spacing-lg);
    background-color: white;
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-sm);
}

/* Contact Page Styles */
.contact-wrapper {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: var(--spacing-2xl);
    align-items: start;
}

.contact-form-section h2 {
    color: var(--primary);
    margin-bottom: var(--spacing-sm);
}

.contact-form-section > p {
    color: var(--text-secondary);
    margin-bottom: var(--spacing-xl);
}

/* Form Styles */
.contact-form {
    background-color: white;
    padding: var(--spacing-xl);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-md);
}

.form-group {
    margin-bottom: var(--spacing-lg);
}

.form-group label {
    display: block;
    margin-bottom: var(--spacing-xs);
    font-weight: 500;
    color: var(--text-primary);
}

.form-group input,
.form-group textarea,
.form-group select {
    width: 100%;
    padding: var(--spacing-sm);
    border: 2px solid var(--soft-grey);
    border-radius: var(--radius-md);
    font-size: 1rem;
    font-family: var(--font-primary);
    transition: border-color 0.3s ease, box-shadow 0.3s ease;
}

.form-group input:focus,
.form-group textarea:focus,
.form-group select:focus {
    outline: none;
    border-color: var(--accent);
    box-shadow: 0 0 0 3px rgba(217, 119, 6, 0.1);
}

.form-group textarea {
    resize: vertical;
    min-height: 100px;
}

.checkbox-group {
    display: flex;
    align-items: flex-start;
}

.checkbox-label {
    display: flex;
    align-items: flex-start;
    cursor: pointer;
    font-size: 0.9rem;
    color: var(--text-secondary);
}

.checkbox-label input[type="checkbox"] {
    width: auto;
    margin-right: var(--spacing-sm);
    margin-top: 0.125rem;
}

.form-submit {
    width: 100%;
    padding: var(--spacing-md);
    font-size: 1.125rem;
}

/* Form Success */
.form-success {
    background-color: white;
    padding: var(--spacing-xl);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-md);
    text-align: center;
}

.success-icon {
    background-color: var(--accent);
    color: white;
    width: 4rem;
    height: 4rem;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    font-weight: bold;
    margin: 0 auto var(--spacing-md);
}

.form-success h3 {
    color: var(--primary);
    margin-bottom: var(--spacing-sm);
}

.form-success p {
    color: var(--text-secondary);
    margin: 0;
}

/* Contact Info Section */
.contact-info-section {
    background-color: var(--bg-light);
    padding: var(--spacing-xl);
    border-radius: var(--radius-lg);
    height: fit-content;
}

.contact-info-section h3 {
    color: var(--primary);
    margin-bottom: var(--spacing-lg);
}

.contact-method {
    display: flex;
    align-items: flex-start;
    margin-bottom: var(--spacing-lg);
}

.contact-icon {
    font-size: 1.5rem;
    margin-right: var(--spacing-md);
    margin-top: 0.25rem;
}

.contact-details h4 {
    color: var(--primary);
    margin-bottom: var(--spacing-xs);
    font-size: 1rem;
}

.contact-details p {
    color: var(--text-secondary);
    margin: 0;
    font-size: 0.9rem;
}

.contact-details a {
    color: var(--primary);
    text-decoration: none;
}

.contact-details a:hover {
    color: var(--accent);
}

/* What to Expect */
.what-to-expect {
    margin-top: var(--spacing-xl);
    padding-top: var(--spacing-lg);
    border-top: 1px solid var(--soft-grey);
}

.what-to-expect h3 {
    color: var(--primary);
    margin-bottom: var(--spacing-md);
}

.expectation-step {
    display: flex;
    align-items: flex-start;
    margin-bottom: var(--spacing-md);
}

.step-number {
    background-color: var(--accent);
    color: white;
    width: 1.5rem;
    height: 1.5rem;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 0.875rem;
    margin-right: var(--spacing-sm);
    flex-shrink: 0;
    margin-top: 0.125rem;
}

.expectation-step p {
    color: var(--text-secondary);
    margin: 0;
    font-size: 0.9rem;
}

/* FAQ Styles */
.faq-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: var(--spacing-lg);
    margin-top: var(--spacing-xl);
}

.faq-item {
    background-color: white;
    padding: var(--spacing-lg);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-sm);
}

.faq-item h3 {
    color: var(--primary);
    margin-bottom: var(--spacing-sm);
    font-size: 1.125rem;
}

.faq-item p {
    color: var(--text-secondary);
    margin: 0;
    font-size: 0.9rem;
    line-height: 1.6;
}

/* Medium screens - adjust navigation spacing */
@media (max-width: 1024px) {
    .nav-menu {
        gap: var(--spacing-sm);
    }

    .nav-link {
        padding: var(--spacing-xs);
        font-size: 0.85rem;
    }

    .nav-link.cta-button {
        padding: var(--spacing-xs) var(--spacing-sm);
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .nav-menu {
        display: none;
    }

    .nav-toggle {
        display: flex;
    }

    .hero .container {
        grid-template-columns: 1fr;
        text-align: center;
    }

    .hero-title {
        font-size: 2rem;
    }

    .section-title {
        font-size: 1.75rem;
    }

    .grid-2,
    .grid-3 {
        grid-template-columns: 1fr;
    }

    .container {
        padding: 0 var(--spacing-sm);
    }

    .contact-wrapper {
        grid-template-columns: 1fr;
        gap: var(--spacing-lg);
    }

    .differentiator,
    .differentiator.reverse {
        grid-template-columns: 1fr;
        text-align: center;
    }

    .differentiator.reverse .diff-content,
    .differentiator.reverse .diff-image {
        order: unset;
    }

    .comparison-header,
    .comparison-row {
        grid-template-columns: 1fr;
        gap: var(--spacing-xs);
    }

    .comparison-column,
    .comparison-cell,
    .comparison-category {
        text-align: left;
        border-right: none;
        border-bottom: 1px solid var(--soft-grey);
    }

    .timeline-item {
        padding-left: 3rem;
    }

    .journey-timeline::before {
        left: 1rem;
    }

    .timeline-marker {
        left: 0.5rem;
    }
}

@media (max-width: 480px) {
    .hero-title {
        font-size: 1.75rem;
    }

    .section-title {
        font-size: 1.5rem;
    }

    .btn {
        padding: var(--spacing-sm);
        font-size: 0.9rem;
    }
}
